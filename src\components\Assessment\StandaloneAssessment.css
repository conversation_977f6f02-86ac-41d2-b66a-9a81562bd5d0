.assessment-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.assessment-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header Styles */
.assessment-header {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.assessment-header h1 {
  margin: 0 0 10px 0;
  font-size: 2rem;
  font-weight: 600;
}

.assessment-description {
  margin: 0 0 20px 0;
  opacity: 0.9;
  line-height: 1.5;
}

/* Progress Bar */
.progress-container {
  margin-top: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: #10b981;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Question Card */
.question-card {
  padding: 40px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.category-badge {
  background: #e0e7ff;
  color: #4f46e5;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.question-number {
  color: #6b7280;
  font-size: 0.9rem;
}

.question-text {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #1f2937;
  margin-bottom: 30px;
  font-weight: 500;
}

/* Answer Options */
.answer-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.answer-option {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.answer-option:hover {
  border-color: #4f46e5;
  background: #f8fafc;
  transform: translateY(-1px);
}

.answer-option.selected {
  border-color: #4f46e5;
  background: #eef2ff;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.option-value {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f3f4f6;
  border-radius: 50%;
  font-weight: 600;
  color: #4b5563;
  margin-right: 16px;
  flex-shrink: 0;
}

.answer-option.selected .option-value {
  background: #4f46e5;
  color: white;
}

.option-label {
  flex: 1;
  font-size: 1rem;
  color: #374151;
}

/* Navigation */
.question-navigation {
  display: flex;
  justify-content: space-between;
  padding: 20px 40px;
  border-top: 1px solid #e5e7eb;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #4338ca;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
  transform: translateY(-1px);
}

/* Assessment Switcher */
.assessment-switcher {
  padding: 30px 40px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.assessment-switcher h3 {
  margin: 0 0 15px 0;
  color: #374151;
  font-size: 1.1rem;
}

.switcher-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.switcher-btn {
  padding: 10px 16px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.switcher-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
}

.switcher-btn.active {
  border-color: #4f46e5;
  background: #4f46e5;
  color: white;
}

/* Completion Card */
.completion-card {
  padding: 60px 40px;
  text-align: center;
}

.completion-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.completion-card h2 {
  margin: 0 0 15px 0;
  color: #1f2937;
  font-size: 2rem;
}

.completion-card p {
  color: #6b7280;
  margin: 0 0 10px 0;
  line-height: 1.5;
}

.completion-actions {
  margin-top: 40px;
}

.assessment-options {
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e5e7eb;
}

.assessment-options h3 {
  margin: 0 0 20px 0;
  color: #374151;
}

.assessment-options .btn {
  margin: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .assessment-page {
    padding: 10px;
  }
  
  .assessment-header {
    padding: 20px;
  }
  
  .assessment-header h1 {
    font-size: 1.5rem;
  }
  
  .question-card {
    padding: 20px;
  }
  
  .question-navigation {
    padding: 15px 20px;
  }
  
  .assessment-switcher {
    padding: 20px;
  }
  
  .switcher-buttons {
    flex-direction: column;
  }
  
  .completion-card {
    padding: 40px 20px;
  }
  
  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
