import React, { useState, useEffect } from 'react';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';
import './StandaloneAssessment.css';

const StandaloneAssessment = () => {
  const [currentAssessment, setCurrentAssessment] = useState('via'); // 'via', 'riasec', 'bigFive'
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [isCompleted, setIsCompleted] = useState(false);
  const [progress, setProgress] = useState(0);

  // Get current assessment data
  const getAssessmentData = () => {
    switch (currentAssessment) {
      case 'via':
        return viaQuestions;
      case 'riasec':
        return riasecQuestions;
      case 'bigFive':
        return bigFiveQuestions;
      default:
        return viaQuestions;
    }
  };

  // Get all questions from current assessment
  const getAllQuestions = () => {
    const assessmentData = getAssessmentData();
    const allQuestions = [];
    
    Object.keys(assessmentData.categories).forEach(categoryKey => {
      const category = assessmentData.categories[categoryKey];
      category.questions.forEach((question, index) => {
        allQuestions.push({
          question,
          category: categoryKey,
          categoryName: category.name,
          questionIndex: index,
          isReverse: false
        });
      });
      
      // Add reverse questions if they exist (for Big Five)
      if (category.reverseQuestions) {
        category.reverseQuestions.forEach((question, index) => {
          allQuestions.push({
            question,
            category: categoryKey,
            categoryName: category.name,
            questionIndex: index,
            isReverse: true
          });
        });
      }
    });
    
    return allQuestions;
  };

  const allQuestions = getAllQuestions();
  const totalQuestions = allQuestions.length;

  // Update progress
  useEffect(() => {
    const answeredQuestions = Object.keys(answers).length;
    setProgress((answeredQuestions / totalQuestions) * 100);
  }, [answers, totalQuestions]);

  // Handle answer selection
  const handleAnswer = (value) => {
    const questionKey = `${currentAssessment}_${currentQuestionIndex}`;
    setAnswers(prev => ({
      ...prev,
      [questionKey]: {
        value,
        question: allQuestions[currentQuestionIndex].question,
        category: allQuestions[currentQuestionIndex].category,
        isReverse: allQuestions[currentQuestionIndex].isReverse
      }
    }));

    // Auto advance to next question
    setTimeout(() => {
      if (currentQuestionIndex < totalQuestions - 1) {
        setCurrentQuestionIndex(prev => prev + 1);
      } else {
        completeAssessment();
      }
    }, 300);
  };

  // Complete current assessment
  const completeAssessment = () => {
    // Save to localStorage
    const assessmentResults = {
      assessmentType: currentAssessment,
      answers: answers,
      completedAt: new Date().toISOString(),
      totalQuestions: totalQuestions
    };

    // Get existing results
    const existingResults = JSON.parse(localStorage.getItem('assessmentResults') || '{}');
    existingResults[currentAssessment] = assessmentResults;
    localStorage.setItem('assessmentResults', JSON.stringify(existingResults));

    setIsCompleted(true);
  };

  // Start new assessment
  const startNewAssessment = (assessmentType) => {
    setCurrentAssessment(assessmentType);
    setCurrentQuestionIndex(0);
    setAnswers({});
    setIsCompleted(false);
  };

  // Continue current assessment
  const continueAssessment = () => {
    setIsCompleted(false);
  };

  // View saved results
  const viewResults = () => {
    const savedResults = JSON.parse(localStorage.getItem('assessmentResults') || '{}');
    console.log('Saved Assessment Results:', savedResults);
    alert('Hasil assessment telah disimpan di localStorage. Lihat console untuk detail lengkap.');
  };

  // Go to previous question
  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Go to next question
  const goToNext = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const assessmentData = getAssessmentData();
  const currentQuestion = allQuestions[currentQuestionIndex];
  const questionKey = `${currentAssessment}_${currentQuestionIndex}`;
  const currentAnswer = answers[questionKey]?.value;

  if (isCompleted) {
    return (
      <div className="assessment-page">
        <div className="assessment-container">
          <div className="completion-card">
            <div className="completion-icon">✅</div>
            <h2>Assessment Selesai!</h2>
            <p>Terima kasih telah menyelesaikan {assessmentData.title}</p>
            <p>Hasil Anda telah disimpan di browser ini.</p>
            
            <div className="completion-actions">
              <button
                className="btn btn-primary"
                onClick={viewResults}
              >
                Lihat Hasil di Console
              </button>
              <button
                className="btn btn-secondary"
                onClick={continueAssessment}
              >
                Kembali ke Assessment
              </button>
              
              <div className="assessment-options">
                <h3>Lakukan Assessment Lainnya:</h3>
                <button 
                  className="btn btn-secondary"
                  onClick={() => startNewAssessment('via')}
                  disabled={currentAssessment === 'via'}
                >
                  VIA Character Strengths
                </button>
                <button 
                  className="btn btn-secondary"
                  onClick={() => startNewAssessment('riasec')}
                  disabled={currentAssessment === 'riasec'}
                >
                  RIASEC Holland Codes
                </button>
                <button 
                  className="btn btn-secondary"
                  onClick={() => startNewAssessment('bigFive')}
                  disabled={currentAssessment === 'bigFive'}
                >
                  Big Five Personality
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="assessment-page">
      <div className="assessment-container">
        {/* Header */}
        <div className="assessment-header">
          <h1>{assessmentData.title}</h1>
          <p className="assessment-description">{assessmentData.description}</p>
          
          {/* Progress Bar */}
          <div className="progress-container">
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <span className="progress-text">
              {currentQuestionIndex + 1} dari {totalQuestions} pertanyaan
            </span>
          </div>
        </div>

        {/* Question Card */}
        <div className="question-card">
          <div className="question-header">
            <span className="category-badge">
              {currentQuestion.categoryName}
            </span>
            <span className="question-number">
              Pertanyaan {currentQuestionIndex + 1}
            </span>
          </div>
          
          <div className="question-text">
            {currentQuestion.question}
          </div>

          {/* Answer Options */}
          <div className="answer-options">
            {assessmentData.scale.map((option) => (
              <button
                key={option.value}
                className={`answer-option ${currentAnswer === option.value ? 'selected' : ''}`}
                onClick={() => handleAnswer(option.value)}
              >
                <span className="option-value">{option.value}</span>
                <span className="option-label">{option.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Navigation */}
        <div className="question-navigation">
          <button 
            className="btn btn-secondary"
            onClick={goToPrevious}
            disabled={currentQuestionIndex === 0}
          >
            ← Sebelumnya
          </button>
          
          <button 
            className="btn btn-secondary"
            onClick={goToNext}
            disabled={currentQuestionIndex === totalQuestions - 1 || !currentAnswer}
          >
            Selanjutnya →
          </button>
        </div>

        {/* Assessment Switcher */}
        <div className="assessment-switcher">
          <h3>Pilih Assessment:</h3>
          <div className="switcher-buttons">
            <button 
              className={`switcher-btn ${currentAssessment === 'via' ? 'active' : ''}`}
              onClick={() => startNewAssessment('via')}
            >
              VIA Character Strengths
            </button>
            <button 
              className={`switcher-btn ${currentAssessment === 'riasec' ? 'active' : ''}`}
              onClick={() => startNewAssessment('riasec')}
            >
              RIASEC Holland Codes
            </button>
            <button 
              className={`switcher-btn ${currentAssessment === 'bigFive' ? 'active' : ''}`}
              onClick={() => startNewAssessment('bigFive')}
            >
              Big Five Personality
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StandaloneAssessment;
